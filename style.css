body {
	position: absolute;
	top : 0px;
	bottom : 0px;
	left: 0px;
	right: 0px;

	margin: 0px;
	overflow: hidden;
	/* background: url(background.png) no-repeat left top fixed; */
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;

}


#video2 {
	width: 100%;
	height: auto;
	defaultPlaybackRate = 1.0;
	position: absolute;


}

#player-container {
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: center;
}


